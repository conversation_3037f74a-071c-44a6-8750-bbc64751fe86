import React, { useEffect, useState } from 'react'
import { observer } from 'mobx-react-lite';
import { SystemSidebar } from './components/SystemSidebar'
import { Sidebar } from './components/Sidebar'
import { SystemToolbar } from './components/SystemToolbar'
import { ResourceManagement } from './pages/ResourceManagement'
import { ProjectProgress } from './pages/ProjectProgress'
import { ProjectOutput } from './pages/ProjectOutput'
import { BudgetManagement } from './pages/BudgetManagement'
import { RiskManagement } from './pages/RiskManagement'
import { HRManagement } from './pages/HRManagement'
import { OrganizationStructure } from './pages/OrganizationStructure'
import { ChangePassword } from './pages/ChangePassword'
import { UserSettings } from './pages/UserSettings'
import { Login } from './pages/Login'
import { Chat } from './components/Chat'
import { MinimizedChat } from './components/MinimizedChat'
import { CloudStorage } from './components/CloudStorage'
import { DesignInput } from './pages/DesignInput'
import { InputReview } from './pages/InputReview'
import { RequirementsBreakdown } from './pages/RequirementsBreakdown'
import { ConfigurationBaseline } from './pages/ConfigurationBaseline'
import { ProjectInput } from './pages/ProjectInput'
import { DesignProposal } from './pages/DesignProposal'
import { DesignReview } from './pages/DesignReview'
import { DesignBaseline } from './pages/DesignBaseline'
import { Repository } from './pages/Repository'
import { navigationStore } from './store/navigationStore'
import { userStore } from './store/userStore'
import { MergeRequests } from './pages/MergeRequests'
import { Submitcode } from './pages/Submitcode'
import { Addproject } from './pages/Addproject'
import { ProjectImport } from './pages/ProjectImport'
import { CICD } from './pages/CICD'
import { DevBaseline } from './pages/DevBaseline'
import {IntegrationTest} from './pages/IntegrationTest'
import { TestPlan } from './pages/TestPlan'
import { TestCase } from './pages/TestCase'
import { TestResult } from './pages/TestResult'
import { IntegrationBaseline } from './pages/IntegrationBaseline'
import ProductAcceptance from './pages/ProductAcceptance'
import ProductDelivery from './pages/ProductDelivery'
import {ProjectClosure} from './pages/ProjectClosure'
import './App.css'
import { Routes, Route, Navigate, Outlet } from 'react-router-dom'
import 'antd/dist/reset.css'

// 开发环境下导入缓存测试工具
if (process.env.NODE_ENV === 'development') {
  import('../utils/organizationCacheTest.js');
}

const App = observer(() => {
  const { currentSystem, currentSubPage } = navigationStore;
  const { currentPage, isAuthenticated } = userStore;
  const [isMinimized, setIsMinimized] = useState(false);

  useEffect(() => {
    // 只在初始加载时检查认证状态
    const initializeAuth = () => {
      const token = localStorage.getItem('token');
      const isAuth = localStorage.getItem('isAuthenticated');

      if (!token || isAuth !== 'true') {
        userStore.clearAuthState();
        userStore.setIsAuthenticated(false);
      } else {
        userStore.setIsAuthenticated(true);
        const savedSystem = localStorage.getItem('currentSystem') || '项目管理';
        const savedSubPage = localStorage.getItem('currentSubPage') || '项目创建';
        navigationStore.setCurrentSystem(savedSystem);
        navigationStore.setCurrentSubPage(savedSubPage);
      }
    };

    initializeAuth();

    // 保存最后访问的路径
    const saveLastPath = () => {
      if (userStore.isAuthenticated) {
        localStorage.setItem('lastPath', window.location.pathname);
      }
    };

    window.addEventListener('beforeunload', saveLastPath);

    return () => {
      window.removeEventListener('beforeunload', saveLastPath);
    };
  }, []); // 只在组件挂载时运行一次

  // 添加刷新页面的处理函数
  const handleRefresh = () => {
    window.location.reload();
  };

  // 添加处理缩小的函数
  const handleMinimize = () => {
    setIsMinimized(true);
  };

  // 添加处理还原的函数
  const handleRestore = () => {
    setIsMinimized(false);
  };

  const MainLayout = () => (
    <div className="flex h-screen overflow-hidden">
      <SystemSidebar />
      <Sidebar />
      <div className="flex-1 h-full overflow-hidden">
        <Outlet />
      </div>
      <SystemToolbar />
      <Chat />
      <MinimizedChat />
      <CloudStorage />
    </div>
  );

  return (
    <Routes>
      {/* 根路径始终首先检查认证状态 */}
      <Route 
        path="/" 
        element={ <Login /> 
        } 
      />
      
      <Route 
        path="index.html" 
        element={
          !isAuthenticated 
            ? <Login /> 
            : <Navigate to="/project-management/create" replace />
        } 
      />
      
      {/* 其他受保护路由 */}
      <Route 
        element={
          !isAuthenticated 
            ? <Navigate to="/" replace /> 
            : <MainLayout />
        }
      >
        {/* 项目管理路由 */}
        <Route path="project-management">
          <Route index element={<Navigate to="create" replace />} />
          <Route path="create" element={<ProjectInput />} />
          <Route path="add" element={<Addproject />} />
          <Route path="input" element={<ProjectImport />} />
          <Route path="output" element={<ProjectOutput />} />
          <Route path="progress" element={<ProjectProgress />} />
          <Route path="resource" element={<ResourceManagement />} />
          <Route path="budget" element={<BudgetManagement />} />
          <Route path="risk" element={<RiskManagement />} />
        </Route>

        {/* 研发管理路由 */}
        <Route path="/dev-management">
          <Route index element={<Navigate to="design-input" replace />} />
          <Route path="design-input" element={<DesignInput />} />
          <Route path="input-review" element={<InputReview />} />
          <Route path="requirements" element={<RequirementsBreakdown />} />
          <Route path="config-baseline" element={<ConfigurationBaseline />} />
          <Route path="design-proposal" element={<DesignProposal />} />
          <Route path="design-review" element={<DesignReview />} />
          <Route path="design-baseline" element={<DesignBaseline />} />
          <Route path="repository" element={<Repository />} />
          <Route path="merge-requests" element={<MergeRequests />} />
          <Route path="code-review" element={<Submitcode />} />
          <Route path="cicd" element={<CICD />} />
          <Route path="dev-baseline" element={<DevBaseline />} />
          <Route path="integration-baseline" element={<IntegrationBaseline />} />
          <Route path="test-plan" element={<TestPlan />} />
          <Route path="test-case" element={<TestCase />} />
          <Route path="test-result" element={<TestResult />} />
          <Route path="integration-test" element={<IntegrationTest />} />
          <Route path="product-acceptance" element={<ProductAcceptance />} />
          <Route path="product-delivery" element={<ProductDelivery />} />
          <Route path="project-closure" element={<ProjectClosure />} />
        </Route>

        {/* 人事管理路由 */}
        <Route path="/hr-management">
          <Route index element={<Navigate to="organization" replace />} />
          <Route path="organization" element={<OrganizationStructure />} />
          <Route path="personnel" element={<HRManagement />} />
        </Route>

        {/* 用户设置路由 */}
        <Route path="/settings">
          <Route path="password" element={<ChangePassword />} />
          <Route path="user" element={<UserSettings />} />
        </Route>
      </Route>

      {/* 处理未匹配路由 */}
      <Route 
        path="*" 
        element={
          !isAuthenticated 
            ? <Navigate to="/" replace /> 
            : <Navigate to="/project-management/create" replace />
        } 
      />
    </Routes>
  );
});

export default App;