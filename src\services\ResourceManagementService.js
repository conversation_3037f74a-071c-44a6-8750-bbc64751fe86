import axios from 'axios';

import { fetchData } from './fetch';


const STAFF_URL = 'http://172.27.1.153:8081';

// 资源类型相关接口
export const resourceTypeApi = {
  // 获取所有资源类型
  getAllTypes: (params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["STAFF_URL"]}/api/resources-types/all?${queryParams}`).then(res => res.json());
  },

  // 创建资源类型
  createType: (typeData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/resources-types`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(typeData)
    }).then(res => res.json()),

  // 更新资源类型
  updateType: (typeId, typeData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/resources-types/${typeId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(typeData)
    }).then(res => res.json()),

  // 删除资源类型
  deleteType: (typeId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/resources-types/${typeId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(res => res.json()),

  // 搜索资源类型
  searchTypes: (name) => {
    const params = new URLSearchParams({ name });
    return fetch(`${fetchData["STAFF_URL"]}/api/resources-types/search?${params}`).then(res => res.json());
  }
};

// 资源管理相关接口
export const resourceApi = {
  // 获取所有资源
  getAllResources: (params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["STAFF_URL"]}/api/resources/all?${queryParams}`).then(res => res.json());
  },

  // 根据部门获取资源
  getResourcesByDepartment: (deptId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/resources/by-department/${deptId}`).then(res => res.json()),

  // 创建资源
  createResource: (resourceData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/resources/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(resourceData)
    }).then(res => res.json()),

  // 更新资源
  updateResource: (resourceId, resourceData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/resources/update/${resourceId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(resourceData)
    }).then(res => res.json()),

  // 删除资源
  deleteResource: (resourceId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/resources/delete/${resourceId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(res => res.json()),

  // 分配资源
  distributeResource: (params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["STAFF_URL"]}/api/resources/distribute?${queryParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(res => res.json());
  },

  // 获取单个资源详情
  getResourceDetail: (resourceId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/resources/get/${resourceId}`).then(res => res.json()),
};

// 组织架构相关接口 - 已迁移到 organizationService.js
// 请使用 import { getAllOrganizations } from '../services/organizationService' 替代
export const organizationApi = {
  // 获取部门数据
  getDepartments: () =>
    fetch(`${fetchData["BASE_URL"]}/api/organizations/department`).then(res => res.json()),
};

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: (params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["STAFF_URL"]}/api/projects/list?${queryParams}`).then(res => res.json());
  },

  // 搜索项目
  searchProjects: (name) => {
    const params = new URLSearchParams({ name });
    return fetch(`${fetchData["STAFF_URL"]}/api/projects/search?${params}`).then(res => res.json());
  }
};

// 人力资源相关接口
export const employeeResourceApi = {
  // 获取员工列表
  getEmployeeList: (organizationId, params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["BASE_URL"]}/api/employees/list/${organizationId}?${queryParams}`).then(res => res.json());
  },

  // 获取单个员工详情
  getEmployeeDetail: (employeeId) => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/one/${employeeId}`).then(res => res.json()),

  // 更新员工信息
  updateEmployee: (employeeId, employeeData) => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/update/${employeeId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(employeeData)
    }).then(res => res.json()),

  // 删除员工
  deleteEmployee: (employeeId) => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/delete/${employeeId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(res => res.json()),

  // 分配员工
  distributeEmployee: (params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["BASE_URL"]}/api/employees/distribute?${queryParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(res => res.json());
  },

  // 搜索员工
  searchEmployees: (name) => {
    const params = new URLSearchParams({ name });
    return fetch(`${fetchData["BASE_URL"]}/api/employees/search?${params}`).then(res => res.json());
  }
}; 