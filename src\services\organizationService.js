// 组织架构相关的 API 服务
import { fetchData } from './fetch';

const API_PREFIX = {
  "organization-service": fetchData["BASE_URL"]
}

// 获取所有组织数据（每次都从服务器获取最新数据）
export const getAllOrganizations = () => {
  return fetch(`${API_PREFIX["organization-service"]}/api/organizations/all`, {
    method: 'GET'
  }).then(res => res.json())
}

// 创建新部门
export const createDepartment = (departmentData) => {
  return fetch(`${API_PREFIX["organization-service"]}/api/organizations/create`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(departmentData)
  }).then(res => res.json())
}

// 更新部门信息
export const updateDepartment = (id, departmentData) => {
  return fetch(`${API_PREFIX["organization-service"]}/api/organizations/update/${id}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(departmentData)
  }).then(res => res.json())
}

// 删除部门
export const deleteDepartment = (id) => {
  return fetch(`${API_PREFIX["organization-service"]}/api/organizations/delete/${id}`, {
    method: 'GET'
  }).then(res => res.json())
}

// 搜索部门
export const searchDepartment = () => {
  return fetch(`${API_PREFIX["organization-service"]}/api/organizations/search-department`, {
    method: 'GET'
  }).then(res => res.json())
}

// 搜索组织
export const searchOrganizations = (keyword) => {
  const params = new URLSearchParams({ keyword });
  return fetch(`${API_PREFIX["organization-service"]}/api/organizations/search?${params}`, {
    method: 'GET'
  }).then(res => res.json())
}

// 检查部门下的员工
export const checkDepartmentEmployees = (organizationId, params = { page: 0, size: 10 }) => {
  const queryParams = new URLSearchParams(params);
  return fetch(`${API_PREFIX["organization-service"]}/api/employees/list/${organizationId}?${queryParams}`, {
    method: 'GET'
  }).then(res => res.json())
}