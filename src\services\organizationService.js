// 组织架构相关的 API 服务
import { fetchData } from './fetch';

const API_PREFIX = {
  "organization-service": fetchData["BASE_URL"]
}

// 添加缓存机制
let organizationCache = null;
let cacheTimestamp = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
let pendingRequest = null; // 防止并发请求

// 获取所有组织数据（带缓存和防重复请求机制）
export const getAllOrganizations = () => {
  const now = Date.now();

  // 如果有缓存且未过期，直接返回缓存数据
  if (organizationCache && cacheTimestamp && (now - cacheTimestamp < CACHE_DURATION)) {
    console.log('使用组织架构缓存数据');
    return Promise.resolve(organizationCache);
  }

  // 如果已有请求在进行中，返回同一个 Promise
  if (pendingRequest) {
    console.log('等待进行中的组织架构请求');
    return pendingRequest;
  }

  // 发起新请求
  console.log('发起新的组织架构请求');
  pendingRequest = fetch(`${API_PREFIX["organization-service"]}/api/organizations/all`, {
    method: 'GET'
  })
  .then(res => res.json())
  .then(data => {
    // 更新缓存
    organizationCache = data;
    cacheTimestamp = now;
    pendingRequest = null; // 清除请求标记
    console.log('组织架构数据已缓存');
    return data;
  })
  .catch(error => {
    pendingRequest = null; // 清除请求标记
    throw error;
  });

  return pendingRequest;
}

// 清除缓存的函数（在数据更新后调用）
export const clearOrganizationCache = () => {
  organizationCache = null;
  cacheTimestamp = null;
  pendingRequest = null;
  console.log('组织架构缓存已清除');
}

// 创建新部门
export const createDepartment = (departmentData) => {
  return fetch(`${API_PREFIX["organization-service"]}/api/organizations/create`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(departmentData)
  })
  .then(res => res.json())
  .then(data => {
    // 创建成功后清除缓存
    clearOrganizationCache();
    return data;
  })
}

// 更新部门信息
export const updateDepartment = (id, departmentData) => {
  return fetch(`${API_PREFIX["organization-service"]}/api/organizations/update/${id}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(departmentData)
  })
  .then(res => res.json())
  .then(data => {
    // 更新成功后清除缓存
    clearOrganizationCache();
    return data;
  })
}

// 删除部门
export const deleteDepartment = (id) => {
  return fetch(`${API_PREFIX["organization-service"]}/api/organizations/delete/${id}`, {
    method: 'GET'
  })
  .then(res => res.json())
  .then(data => {
    // 删除成功后清除缓存
    clearOrganizationCache();
    return data;
  })
}

// 搜索部门
export const searchDepartment = () => {
  return fetch(`${API_PREFIX["organization-service"]}/api/organizations/search-department`, {
    method: 'GET'
  }).then(res => res.json())
}

// 搜索组织
export const searchOrganizations = (keyword) => {
  const params = new URLSearchParams({ keyword });
  return fetch(`${API_PREFIX["organization-service"]}/api/organizations/search?${params}`, {
    method: 'GET'
  }).then(res => res.json())
}

// 检查部门下的员工
export const checkDepartmentEmployees = (organizationId, params = { page: 0, size: 10 }) => {
  const queryParams = new URLSearchParams(params);
  return fetch(`${API_PREFIX["organization-service"]}/api/employees/list/${organizationId}?${queryParams}`, {
    method: 'GET'
  }).then(res => res.json())
}