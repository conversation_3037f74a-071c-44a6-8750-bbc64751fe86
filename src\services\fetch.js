import { message } from 'antd'
import axios from 'axios'

// API URLs configuration
export const fetchData = {
    BASE_URL: 'http://172.27.1.172:8085',
    PROJECT_URL:'http://172.27.1.172:8085',
    STAFF_URL:'http://172.27.1.172:8085',
    GITLAB_URL:'http://172.27.1.61',
    API_BASE_URL : 'http://172.27.1.61/api/v4'
}

// Create axios instance with default config
const service = axios.create({
  baseURL: fetchData.BASE_URL, // Use BASE_URL as default
  timeout: 15000, // Request timeout
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})

// Request interceptor
service.interceptors.request.use(
  config => {
    // You can add token here
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
service.interceptors.response.use(
  response => {
    console.log(response)
    const res = response.data
    if (res.code === 400) {
      message.error(res.message)
      return Promise.reject(new Error(res.message || 'Error'))
    }

    if (res.code === 200) {
      return res.data
    } else if (res.code === undefined) {
      return res
    } else {
      // Handle error response
      console.error('Response error:', res.message || 'Error')
      return Promise.reject(new Error(res.message || 'Error'))
    }
  },
  error => {
    // Handle HTTP errors
    if (error.response) {
      switch (error.response.status) {
        case 400:
          message.error(error.message)
          break
        case 401:
          // Handle unauthorized
          break
        case 403:
          // Handle forbidden
          break
        case 404:
          message.error(error.message)
          break
        case 500:
          if(error.response.data.code === 401){
            message.error(error.response.data.message)
            setTimeout(() => {
              window.location.href = '/login'
            }, 1000);
            return
          }
          message.error('服务器异常，请联系管理员')
          break
        default:
        // Handle other errors
      }
    }
    return Promise.reject(error)
  }
)

export default function fetch (options) {
  // Create new cancel token
  const source = axios.CancelToken.source();
  window.cancelToken = source.cancel;

  options.cancelToken = source.token

  return service
    .request(options)
    .then(res => {
      return Promise.resolve(res)
    })
    .catch(error => {
      if(axios.isCancel(error)){
        console.log("Request canceled", error.message)
      }
      return Promise.reject(error)
    })
}
