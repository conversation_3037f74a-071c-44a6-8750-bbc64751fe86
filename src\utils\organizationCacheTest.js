// 组织架构缓存测试工具
import { getAllOrganizations, clearOrganizationCache } from '../services/organizationService';

/**
 * 测试组织架构缓存机制
 */
export const testOrganizationCache = async () => {
  console.log('=== 开始测试组织架构缓存机制 ===');
  
  try {
    // 清除现有缓存
    clearOrganizationCache();
    console.log('1. 已清除现有缓存');
    
    // 第一次请求 - 应该发起网络请求
    console.log('2. 发起第一次请求...');
    const start1 = Date.now();
    const data1 = await getAllOrganizations();
    const end1 = Date.now();
    console.log(`   第一次请求完成，耗时: ${end1 - start1}ms`);
    
    // 第二次请求 - 应该使用缓存
    console.log('3. 发起第二次请求...');
    const start2 = Date.now();
    const data2 = await getAllOrganizations();
    const end2 = Date.now();
    console.log(`   第二次请求完成，耗时: ${end2 - start2}ms`);
    
    // 验证数据一致性
    const isDataSame = JSON.stringify(data1) === JSON.stringify(data2);
    console.log(`4. 数据一致性检查: ${isDataSame ? '通过' : '失败'}`);
    
    // 并发请求测试
    console.log('5. 测试并发请求...');
    clearOrganizationCache();
    const concurrentStart = Date.now();
    const promises = Array(5).fill().map(() => getAllOrganizations());
    const results = await Promise.all(promises);
    const concurrentEnd = Date.now();
    console.log(`   并发请求完成，耗时: ${concurrentEnd - concurrentStart}ms`);
    
    // 验证并发请求结果一致性
    const allSame = results.every(result => 
      JSON.stringify(result) === JSON.stringify(results[0])
    );
    console.log(`6. 并发请求一致性检查: ${allSame ? '通过' : '失败'}`);
    
    console.log('=== 缓存机制测试完成 ===');
    return {
      success: true,
      firstRequestTime: end1 - start1,
      secondRequestTime: end2 - start2,
      dataConsistency: isDataSame,
      concurrentRequestTime: concurrentEnd - concurrentStart,
      concurrentConsistency: allSame
    };
    
  } catch (error) {
    console.error('缓存测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 在浏览器控制台中运行测试
 * 使用方法：在浏览器控制台中输入 window.testOrgCache()
 */
if (typeof window !== 'undefined') {
  window.testOrgCache = testOrganizationCache;
}
